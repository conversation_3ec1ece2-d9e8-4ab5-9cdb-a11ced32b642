Progetto: Piattaforma di Digitalizzazione e Valorizzazione della Filiera Agricola Locale 

Il progetto mira a creare una piattaforma che permetta la gestione, valorizzazione e tracciabilità dei prodotti agricoli di un territorio comunale. La piattaforma consentirà di caricare, visualizzare e condividere informazioni legate alla filiera agricola, includendo dati relativi alla produzione, trasformazione e vendita di prodotti locali. Sarà pensata come uno strumento per promuovere il territorio, permettendo a chiunque di comprendere la provenienza e la qualità dei prodotti tipici. Inoltre, la piattaforma faciliterà l’organizzazione di eventi locali, come fiere e visite guidate alle aziende. 

La piattaforma permette la tracciabilità di ogni prodotto attraverso i diversi attori della filiera, visualizzando l’intero ciclo produttivo. Il caricamento di informazioni e contenuti sarà riferito a specifici punti della filiera, come aziende agricole e mercati sul territorio comunale. Ad esempio, i contenuti potranno includere dati relativi a certificazioni, metodi di coltivazione e pratiche di produzione. La piattaforma dovrà inoltre fornire la possibilità di visualizzare tutti i punti della filiera su una mappa interattiva. 

Si immaginano almeno i seguenti attori: 

Produttore: può caricare informazioni sui propri prodotti (metodi di coltivazione, certificazioni ecc.) e venderli nel marketplace. 

Trasformatore: può caricare informazioni sui processi di trasformazione dei prodotti e sulle certificazioni di qualità, con la possibilità di collegare le fasi della produzione ai produttori locali. Può vendere i prodotti nel marketplace. 

Distributore di Tipicità: può caricare informazioni relative ai prodotti in vendita. Questi prodotti possono essere venduti singolarmente tramite il marketplace. Inoltre, il distributore può creare pacchetti di prodotti che combinano più articoli in un’unica offerta, favorendo l’acquisto di esperienze gastronomiche legate al territorio. 

Curatore: verifica i contenuti caricati dai produttori, trasformatori e venditori. Gestisce la validità delle informazioni e le approva prima della pubblicazione. 

Animatore della Filiera: può organizzare eventi e fiere invitando produttori, trasformatori e venditori. Inoltre, può proporre visite alle aziende, come eventi di presentazione e tour di degustazione. 

Acquirente: accede alla piattaforma per informarsi sulla provenienza dei prodotti e può acquistare dai vari attori della filiera. Può inoltre prenotare la partecipazione a fiere ed eventi. 

Utente Generico: accede ai contenuti per informarsi sulla provenienza e la qualità dei prodotti locali. 

Gestore della Piattaforma: ha la responsabilità di gestire gli aspetti amministrativi della piattaforma, le autorizzazioni e gli accrediti per gli attori coinvolti. 

Sistemi Social destinatari di contenuti:  gli utenti che creano contenuti possono condividerli sui propri canali social. 

Sistema OSM: fornisce informazioni sulle mappe del territorio e la visualizzazione dei punti della filiera agricola. 

 

Vincoli e Dettagli Tecnici 

Il progetto deve essere sviluppato in Java e successivamente portato su Spring Boot. 

Lo strato di presentazione può essere sviluppato con strumenti a scelta dello studente ed eventualmente può limitarsi alla linea di comando e/o API REST. 

Si raccomanda di utilizzare almeno due design pattern (diverso dal Singleton). 